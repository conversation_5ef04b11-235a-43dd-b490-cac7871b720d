import { vi } from 'vitest';
import PaymentService from '../../services/paymentService';
import { api } from '../../services/api';

// Mock the API
vi.mock('../../services/api');
const mockedApi = api as any;

// Mock Stripe
const mockStripe = {
  confirmCardPayment: vi.fn(),
};

vi.mock('@stripe/stripe-js', () => ({
  loadStripe: vi.fn(() => Promise.resolve(mockStripe)),
}));

describe('PaymentService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createPaymentIntent', () => {
    it('should create payment intent successfully', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            paymentIntent: {
              id: 'pi_test123',
              amount: 2000,
              currency: 'usd',
              status: 'requires_payment_method',
              client_secret: 'pi_test123_secret',
            },
          },
        },
      };

      mockedApi.post.mockResolvedValue(mockResponse);

      const request = {
        amount: 2000,
        currency: 'usd',
        orderId: 'order-123',
        metadata: { userId: 'user-456' },
      };

      const result = await PaymentService.createPaymentIntent(request);

      expect(mockedApi.post).toHaveBeenCalledWith('/payments/create-intent', {
        amount: 2000,
        currency: 'usd',
        orderId: 'order-123',
        metadata: { userId: 'user-456' },
      });

      expect(result).toEqual(mockResponse.data);
    });

    it('should use default currency when not provided', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            paymentIntent: {
              id: 'pi_test123',
              amount: 1000,
              currency: 'usd',
              status: 'requires_payment_method',
              client_secret: 'pi_test123_secret',
            },
          },
        },
      };

      mockedApi.post.mockResolvedValue(mockResponse);

      const request = {
        amount: 1000,
      };

      await PaymentService.createPaymentIntent(request);

      expect(mockedApi.post).toHaveBeenCalledWith('/payments/create-intent', {
        amount: 1000,
        currency: 'usd',
        orderId: undefined,
        metadata: undefined,
      });
    });

    it('should handle API errors', async () => {
      const mockError = new Error('API Error');
      mockedApi.post.mockRejectedValue(mockError);

      const request = { amount: 2000 };

      await expect(PaymentService.createPaymentIntent(request)).rejects.toThrow('API Error');
    });
  });

  describe('confirmPayment', () => {
    it('should confirm payment successfully', async () => {
      const mockPaymentIntent = {
        id: 'pi_test123',
        status: 'succeeded',
        amount: 2000,
        currency: 'usd',
      };

      mockStripe.confirmCardPayment.mockResolvedValue({
        paymentIntent: mockPaymentIntent,
        error: null,
      });

      const result = await PaymentService.confirmPayment('pi_test123_secret', 'pm_test456');

      expect(mockStripe.confirmCardPayment).toHaveBeenCalledWith('pi_test123_secret', {
        payment_method: 'pm_test456',
      });

      expect(result).toEqual({
        success: true,
        paymentIntent: mockPaymentIntent,
      });
    });

    it('should handle Stripe errors', async () => {
      const mockError = {
        message: 'Your card was declined.',
        type: 'card_error',
        code: 'card_declined',
      };

      mockStripe.confirmCardPayment.mockResolvedValue({
        paymentIntent: null,
        error: mockError,
      });

      const result = await PaymentService.confirmPayment('pi_test123_secret', 'pm_test456');

      expect(result).toEqual({
        success: false,
        error: 'Your card was declined.',
      });
    });

    it('should handle when Stripe is not initialized', async () => {
      // Mock getStripe to return null
      vi.spyOn(PaymentService, 'getStripe').mockResolvedValue(null);

      const result = await PaymentService.confirmPayment('pi_test123_secret', 'pm_test456');

      expect(result).toEqual({
        success: false,
        error: 'Stripe not initialized',
      });
    });

    it('should handle unexpected errors', async () => {
      mockStripe.confirmCardPayment.mockRejectedValue(new Error('Network error'));

      const result = await PaymentService.confirmPayment('pi_test123_secret', 'pm_test456');

      expect(result).toEqual({
        success: false,
        error: 'Network error',
      });
    });
  });

  describe('processPayment', () => {
    const mockCardElement = {
      mount: vi.fn(),
      unmount: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
    };

    it('should process payment successfully', async () => {
      const mockPaymentIntent = {
        id: 'pi_test123',
        status: 'succeeded',
        amount: 2000,
        currency: 'usd',
      };

      mockStripe.confirmCardPayment.mockResolvedValue({
        paymentIntent: mockPaymentIntent,
        error: null,
      });

      const result = await PaymentService.processPayment('pi_test123_secret', mockCardElement);

      expect(mockStripe.confirmCardPayment).toHaveBeenCalledWith('pi_test123_secret', {
        payment_method: {
          card: mockCardElement,
        },
      });

      expect(result).toEqual({
        success: true,
        paymentIntent: mockPaymentIntent,
      });
    });

    it('should handle payment processing errors', async () => {
      const mockError = {
        message: 'Insufficient funds.',
        type: 'card_error',
        code: 'insufficient_funds',
      };

      mockStripe.confirmCardPayment.mockResolvedValue({
        paymentIntent: null,
        error: mockError,
      });

      const result = await PaymentService.processPayment('pi_test123_secret', mockCardElement);

      expect(result).toEqual({
        success: false,
        error: 'Insufficient funds.',
      });
    });
  });

  describe('getPaymentMethods', () => {
    it('should get payment methods successfully', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            paymentMethods: [
              {
                id: 'pm_test123',
                type: 'card',
                card: {
                  brand: 'visa',
                  last4: '4242',
                  exp_month: 12,
                  exp_year: 2025,
                },
              },
            ],
          },
        },
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const result = await PaymentService.getPaymentMethods('cus_test123');

      expect(mockedApi.get).toHaveBeenCalledWith('/payments/payment-methods/cus_test123');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('savePaymentMethod', () => {
    it('should save payment method successfully', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            paymentMethod: {
              id: 'pm_test123',
              type: 'card',
              card: {
                brand: 'visa',
                last4: '4242',
              },
            },
          },
        },
      };

      mockedApi.post.mockResolvedValue(mockResponse);

      const result = await PaymentService.savePaymentMethod('cus_test123', 'pm_test456');

      expect(mockedApi.post).toHaveBeenCalledWith('/payments/save-payment-method', {
        customerId: 'cus_test123',
        paymentMethodId: 'pm_test456',
      });

      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('processRefund', () => {
    it('should process refund successfully', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            refund: {
              id: 're_test123',
              amount: 1000,
              status: 'succeeded',
              payment_intent: 'pi_test123',
            },
          },
        },
      };

      mockedApi.post.mockResolvedValue(mockResponse);

      const result = await PaymentService.processRefund('pi_test123', 1000);

      expect(mockedApi.post).toHaveBeenCalledWith('/payments/refund', {
        paymentIntentId: 'pi_test123',
        amount: 1000,
      });

      expect(result).toEqual(mockResponse.data);
    });

    it('should process full refund when amount not provided', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            refund: {
              id: 're_test123',
              amount: 2000,
              status: 'succeeded',
              payment_intent: 'pi_test123',
            },
          },
        },
      };

      mockedApi.post.mockResolvedValue(mockResponse);

      await PaymentService.processRefund('pi_test123');

      expect(mockedApi.post).toHaveBeenCalledWith('/payments/refund', {
        paymentIntentId: 'pi_test123',
        amount: undefined,
      });
    });
  });

  describe('getPaymentHistory', () => {
    it('should get payment history successfully', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: {
            payments: [
              {
                id: 'pi_test123',
                amount: 2000,
                currency: 'usd',
                status: 'succeeded',
                created: 1234567890,
              },
              {
                id: 'pi_test456',
                amount: 1500,
                currency: 'usd',
                status: 'succeeded',
                created: 1234567800,
              },
            ],
          },
        },
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const result = await PaymentService.getPaymentHistory('cus_test123', 5);

      expect(mockedApi.get).toHaveBeenCalledWith('/payments/history/cus_test123?limit=5');
      expect(result).toEqual(mockResponse.data);
    });

    it('should use default limit when not provided', async () => {
      const mockResponse = {
        data: {
          status: 'success',
          data: { payments: [] },
        },
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      await PaymentService.getPaymentHistory('cus_test123');

      expect(mockedApi.get).toHaveBeenCalledWith('/payments/history/cus_test123?limit=10');
    });
  });
});
