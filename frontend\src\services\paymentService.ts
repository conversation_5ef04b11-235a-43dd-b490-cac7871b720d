import { loadStripe, Stripe } from '@stripe/stripe-js';
import { api } from './api';
import type { ApiResponse } from './api';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '');

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret: string;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency?: string;
  orderId?: string;
  metadata?: Record<string, string>;
}

export interface PaymentResult {
  success: boolean;
  paymentIntent?: PaymentIntent;
  error?: string;
}

export class PaymentService {
  private static stripe: Stripe | null = null;

  static async getStripe(): Promise<Stripe | null> {
    if (!this.stripe) {
      this.stripe = await stripePromise;
    }
    return this.stripe;
  }

  // Create payment intent on the backend
  static async createPaymentIntent(
    request: CreatePaymentIntentRequest
  ): Promise<ApiResponse<{ paymentIntent: PaymentIntent }>> {
    const response = await api.post<ApiResponse<{ paymentIntent: PaymentIntent }>>(
      '/payments/create-intent',
      {
        amount: request.amount,
        currency: request.currency || 'usd',
        orderId: request.orderId,
        metadata: request.metadata,
      }
    );
    return response.data;
  }

  // Confirm payment
  static async confirmPayment(
    clientSecret: string,
    paymentMethodId: string
  ): Promise<PaymentResult> {
    try {
      const stripe = await this.getStripe();
      if (!stripe) {
        throw new Error('Stripe not initialized');
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: paymentMethodId,
      });

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        paymentIntent: paymentIntent as PaymentIntent,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Payment confirmation failed',
      };
    }
  }

  // Process payment with card element
  static async processPayment(
    clientSecret: string,
    cardElement: any
  ): Promise<PaymentResult> {
    try {
      const stripe = await this.getStripe();
      if (!stripe) {
        throw new Error('Stripe not initialized');
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        },
      });

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        paymentIntent: paymentIntent as PaymentIntent,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Payment processing failed',
      };
    }
  }

  // Get payment methods for a customer
  static async getPaymentMethods(customerId: string): Promise<ApiResponse<{ paymentMethods: any[] }>> {
    const response = await api.get<ApiResponse<{ paymentMethods: any[] }>>(
      `/payments/payment-methods/${customerId}`
    );
    return response.data;
  }

  // Save payment method
  static async savePaymentMethod(
    customerId: string,
    paymentMethodId: string
  ): Promise<ApiResponse<{ paymentMethod: any }>> {
    const response = await api.post<ApiResponse<{ paymentMethod: any }>>(
      '/payments/save-payment-method',
      {
        customerId,
        paymentMethodId,
      }
    );
    return response.data;
  }

  // Process refund
  static async processRefund(
    paymentIntentId: string,
    amount?: number
  ): Promise<ApiResponse<{ refund: any }>> {
    const response = await api.post<ApiResponse<{ refund: any }>>(
      '/payments/refund',
      {
        paymentIntentId,
        amount,
      }
    );
    return response.data;
  }

  // Get payment history
  static async getPaymentHistory(
    customerId: string,
    limit = 10
  ): Promise<ApiResponse<{ payments: PaymentIntent[] }>> {
    const response = await api.get<ApiResponse<{ payments: PaymentIntent[] }>>(
      `/payments/history/${customerId}?limit=${limit}`
    );
    return response.data;
  }
}

export default PaymentService;
